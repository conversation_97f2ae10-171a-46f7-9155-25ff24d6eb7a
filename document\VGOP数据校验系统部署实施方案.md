# VGOP数据校验系统部署实施方案

| 项目 | 内容 |
|------|------|
| 文档名称 | VGOP数据校验系统部署实施方案 |
| 部门名称 | 数据平台部 | 编写人 | 系统架构师 | 编写日期 | 2025/01/05 |
| 项目编号 | VGOP-DEPLOY-001 | 审核人 | 技术负责人 | 审核日期 | 2025/01/06 |

*[该文档为VGOP数据校验系统首次生产环境部署的完整实施方案。*
*本文档包含'软件概述'、'部署环境信息'、'装载实施步骤'、'详细装载步骤'、'部署验证'、'倒回步骤'等核心部分。]*

## 软件概述

### 部署包对应项目信息
**项目编号**: VGOP-DEPLOY-001

| 序号 | 软件模块 | 当前版本 | 部署版本 | 功能描述 |
|------|----------|----------|----------|----------|
| 1. | VGOP数据校验系统 | 无（新部署） | v1.0.0 | 替代Shell脚本，实现数据导出、校验、传输的全自动化处理 |
| 2. | JDK运行环境 | 无 | JDK 1.8.0_311 | 提供Java应用运行环境 |
| 3. | 数据库表结构 | 无 | v1.0.0 | 创建告警表、统计表、执行日志表等业务表 |
| 4. | 系统配置文件 | 无 | v1.0.0 | 生产环境数据库连接、SFTP配置等 |

*[VGOP数据校验系统是基于Spring Boot 2.5.5的微服务应用，采用三阶段处理架构：数据导出→数据校验→文件传输，支持15个业务接口的自动化处理。]*

## 部署对现网的影响

### 影响评估
- **业务影响**: 无直接影响，系统为新增功能模块
- **数据库影响**: 需要创建新的业务表，不影响现有表结构
- **网络影响**: 需要配置SFTP连接，不影响现有网络架构
- **性能影响**: 新增系统资源消耗，建议在业务低峰期部署

## 软件部署环境

### 环境信息
| 环境信息项目 | 信息内容 | 备注 |
|-------------|----------|------|
| 操作系统版本 | Linux CentOS 7.x / Red Hat 7.x | 64位系统 |
| JDK版本 | JDK 1.8.0_311 或更高版本 | 需要新安装 |
| 数据库版本 | Informix 12.10 | 已存在，需要创建新表 |
| 应用服务器 | 内置Tomcat（Spring Boot） | 端口8080 |
| SFTP服务器 | 已配置，连接信息待确认 | 需要测试连通性 |
| 内存要求 | 最低4GB，推荐8GB | 用于大数据量处理 |
| 磁盘空间 | 最低50GB可用空间 | 用于数据文件存储 |
| 网络要求 | 内网连接稳定 | 数据库和SFTP连接 |

*[填写说明：生产环境信息需要运维团队确认具体的服务器配置和网络环境。]*

## 装载实施步骤

### 装载步骤说明
此次部署的操作步骤如下：

| 序号 | 任务 | 预计操作时长（分钟） | 备注 |
|------|------|---------------------|------|
| 1. | 环境准备和JDK安装 | 15 | 安装JDK并配置环境变量 |
| 2. | 创建应用目录和用户 | 10 | 创建专用用户和目录结构 |
| 3. | 数据库表结构创建 | 20 | 执行DDL脚本创建业务表 |
| 4. | 应用文件部署 | 10 | 上传jar包和配置文件 |
| 5. | 配置文件修改 | 15 | 修改生产环境配置参数 |
| 6. | 系统服务配置 | 10 | 配置systemd服务 |
| 7. | 应用启动和验证 | 15 | 启动服务并验证功能 |
| 8. | 连通性测试 | 10 | 测试数据库和SFTP连接 |

**总计预估时间**: 105分钟（约1小时45分钟）

*[填写说明：实际操作时间可能因环境差异而有所变化，建议预留额外30分钟的缓冲时间。]*

## 详细装载步骤

### 1. 环境准备和JDK安装

**操作对象**: 生产服务器  
**操作行为**: 新增JDK环境

**修改步骤如下**:

```bash
# 1.1 检查当前Java环境
java -version
echo $JAVA_HOME

# 1.2 下载并安装JDK 1.8
cd /opt
wget https://download.oracle.com/otn/java/jdk/8u311-b11/4d5417147a92418ea8b615e228bb6935/jdk-8u311-linux-x64.tar.gz
tar -xzf jdk-8u311-linux-x64.tar.gz
mv jdk1.8.0_311 java

# 1.3 配置环境变量
echo 'export JAVA_HOME=/opt/java' >> /etc/profile
echo 'export PATH=$JAVA_HOME/bin:$PATH' >> /etc/profile
source /etc/profile

# 1.4 验证安装
java -version
javac -version
```

### 2. 创建应用目录和用户

**操作对象**: 生产服务器  
**操作行为**: 新增应用用户和目录

```bash
# 2.1 创建应用用户
useradd -m -s /bin/bash vgop
passwd vgop

# 2.2 创建应用目录结构
mkdir -p /opt/vgop/{bin,config,logs,data,backup}
mkdir -p /opt/vgop/data/{VGOPdata/datafile/{day,month},alerts,export}

# 2.3 设置目录权限
chown -R vgop:vgop /opt/vgop
chmod -R 755 /opt/vgop
```

### 3. 数据库表结构创建

**操作对象**: Informix数据库  
**操作行为**: 新增业务表

```bash
# 3.1 连接数据库并执行建表脚本
dbaccess bms << EOF
-- 创建告警信息表
CREATE TABLE vgop_validation_alerts (
    alert_id SERIAL8 PRIMARY KEY,
    alert_time DATETIME YEAR TO FRACTION(3) DEFAULT CURRENT YEAR TO FRACTION(3) NOT NULL,
    interface_name VARCHAR(100) NOT NULL,
    alert_type VARCHAR(50) NOT NULL,
    alert_level VARCHAR(20) DEFAULT 'WARNING' NOT NULL,
    alert_message VARCHAR(255) NOT NULL,
    file_name VARCHAR(200),
    line_number INT8,
    error_data TEXT,
    field_errors TEXT,
    metric_details TEXT,
    excel_report_path VARCHAR(255),
    status VARCHAR(20) DEFAULT 'NEW' NOT NULL,
    handled_by VARCHAR(100),
    handled_time DATETIME YEAR TO FRACTION(3)
);

-- 创建历史统计表
CREATE TABLE vgop_metrics_history (
    history_id SERIAL8 PRIMARY KEY,
    processing_date DATE NOT NULL,
    interface_name VARCHAR(100) NOT NULL,
    total_records INT8 DEFAULT 0 NOT NULL,
    compliant_records INT8 DEFAULT 0 NOT NULL,
    non_compliant_records INT8 DEFAULT 0 NOT NULL,
    export_file_count INTEGER DEFAULT 0 NOT NULL,
    export_status VARCHAR(20) NOT NULL,
    transfer_status VARCHAR(20),
    created_at DATETIME YEAR TO FRACTION(3) DEFAULT CURRENT YEAR TO FRACTION(3) NOT NULL,
    UNIQUE (interface_name, processing_date) CONSTRAINT uk_interface_date
);

-- 创建任务执行日志表
CREATE TABLE vgop_task_execution_log (
    log_id SERIAL8 PRIMARY KEY,
    task_id VARCHAR(50) NOT NULL,
    task_name VARCHAR(100) NOT NULL,
    task_type VARCHAR(20) NOT NULL,
    start_time DATETIME YEAR TO FRACTION(3) NOT NULL,
    end_time DATETIME YEAR TO FRACTION(3),
    status VARCHAR(20) NOT NULL,
    error_message TEXT,
    processed_records INT8,
    generated_files INTEGER
);

-- 创建索引
CREATE INDEX idx_alerts_time ON vgop_validation_alerts(alert_time);
CREATE INDEX idx_alerts_interface ON vgop_validation_alerts(interface_name);
CREATE INDEX idx_task_log_task_id ON vgop_task_execution_log(task_id);

EOF

# 3.2 验证表创建
dbaccess bms << EOF
SELECT tabname FROM systables WHERE tabname LIKE 'vgop_%';
EOF
```

### 4. 应用文件部署

**操作对象**: 应用服务器  
**操作行为**: 新增应用文件

```bash
# 4.1 切换到vgop用户
su - vgop

# 4.2 上传应用文件（由开发团队提供）
# 将vgop-service.jar复制到/opt/vgop/bin/目录
cp vgop-service.jar /opt/vgop/bin/

# 4.3 上传配置文件
cp application-prod.yml /opt/vgop/config/
cp logback-spring.xml /opt/vgop/config/

# 4.4 设置执行权限
chmod +x /opt/vgop/bin/vgop-service.jar
```

### 5. 配置文件修改

**操作对象**: 应用配置文件  
**操作行为**: 修改生产环境配置

```bash
# 5.1 编辑生产环境配置文件
vi /opt/vgop/config/application-prod.yml
```

**配置文件内容**:
```yaml
# 生产环境配置
spring:
  profiles:
    active: prod
  datasource:
    url: jdbc:informix-sqli://[生产数据库IP]:[端口]/bms:INFORMIXSERVER=[服务器名]
    username: [数据库用户名]
    password: [数据库密码]
    driver-class-name: com.informix.jdbc.IfxDriver

# VGOP应用配置
app:
  schedules:
    daily-cron: "0 0 0 * * ?"    # 每日凌晨0点
    monthly-cron: "0 0 5 1 * ?"  # 每月1日凌晨5点
  
  base-path:
    export-root: "/opt/vgop/data/VGOPdata/datafile/"
    log-root: "/opt/vgop/logs/"
    backup-root: "/opt/vgop/backup/"
  
  sftp:
    host: "[SFTP服务器IP]"
    port: [SFTP端口]
    username: "[SFTP用户名]"
    password: "[SFTP密码]"
    remote-base-path: "/upload/"
    connection-timeout: 30000
    retry-times: 3

# 日志配置
logging:
  file:
    name: /opt/vgop/logs/vgop-service.log
    max-size: 100MB
    max-history: 30
```

### 6. 系统服务配置

**操作对象**: 系统服务  
**操作行为**: 新增systemd服务

```bash
# 6.1 创建systemd服务文件
sudo vi /etc/systemd/system/vgop.service
```

**服务配置内容**:
```ini
[Unit]
Description=VGOP Data Validation Service
After=network.target

[Service]
Type=forking
User=vgop
Group=vgop
ExecStart=/opt/java/bin/java -jar -Xms2g -Xmx4g -Dspring.profiles.active=prod -Dspring.config.location=/opt/vgop/config/ /opt/vgop/bin/vgop-service.jar
ExecStop=/bin/kill -TERM $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

```bash
# 6.2 重载systemd配置
sudo systemctl daemon-reload
sudo systemctl enable vgop
```

### 7. 应用启动和验证

**操作对象**: VGOP应用服务
**操作行为**: 启动服务并验证

```bash
# 7.1 启动VGOP服务
sudo systemctl start vgop

# 7.2 检查服务状态
sudo systemctl status vgop

# 7.3 查看启动日志
tail -f /opt/vgop/logs/vgop-service.log

# 7.4 验证端口监听
netstat -tlnp | grep 8080
ss -tlnp | grep 8080

# 7.5 检查进程
ps -ef | grep vgop-service
```

### 8. 连通性测试

**操作对象**: 外部系统连接
**操作行为**: 测试数据库和SFTP连接

```bash
# 8.1 测试数据库连接
dbaccess bms << EOF
SELECT COUNT(*) FROM systables WHERE tabname = 'vgop_validation_alerts';
EOF

# 8.2 测试SFTP连接
sftp [SFTP用户名]@[SFTP服务器IP] << EOF
pwd
ls
quit
EOF

# 8.3 测试应用健康检查接口
curl -X GET http://localhost:8080/vgop/actuator/health

# 8.4 测试应用API接口
curl -X GET http://localhost:8080/vgop/api/monitor/status
```

## 部署验证

### 验证步骤

#### 1. 基础环境验证

**验证项目**: JDK环境和应用部署
**验证方法**:
```bash
# 验证JDK版本
java -version
# 预期结果: java version "1.8.0_311"

# 验证应用文件
ls -la /opt/vgop/bin/vgop-service.jar
# 预期结果: 文件存在且有执行权限

# 验证配置文件
ls -la /opt/vgop/config/application-prod.yml
# 预期结果: 配置文件存在
```

#### 2. 数据库验证

**验证项目**: 数据库表创建和连接
**验证方法**:
```bash
# 验证表结构
dbaccess bms << EOF
SELECT tabname FROM systables WHERE tabname LIKE 'vgop_%' ORDER BY tabname;
EOF
# 预期结果: 显示vgop_validation_alerts, vgop_metrics_history, vgop_task_execution_log

# 验证表权限
dbaccess bms << EOF
SELECT COUNT(*) FROM vgop_validation_alerts;
EOF
# 预期结果: 返回0（空表）
```

#### 3. 应用服务验证

**验证项目**: 应用启动和基础功能
**验证方法**:
```bash
# 验证服务状态
sudo systemctl status vgop
# 预期结果: Active: active (running)

# 验证健康检查
curl -X GET http://localhost:8080/vgop/actuator/health
# 预期结果: {"status":"UP"}

# 验证应用信息
curl -X GET http://localhost:8080/vgop/actuator/info
# 预期结果: 返回应用版本信息
```

#### 4. 功能验证

**验证项目**: 核心业务功能
**验证方法**:
```bash
# 验证数据库连接功能
curl -X GET http://localhost:8080/vgop/api/monitor/database
# 预期结果: 数据库连接正常

# 验证SFTP连接功能
curl -X GET http://localhost:8080/vgop/api/monitor/sftp
# 预期结果: SFTP连接正常

# 验证配置加载
curl -X GET http://localhost:8080/vgop/api/monitor/config
# 预期结果: 配置信息正确加载
```

#### 5. 日志验证

**验证项目**: 日志输出和记录
**验证方法**:
```bash
# 检查应用日志
tail -n 50 /opt/vgop/logs/vgop-service.log
# 预期结果: 无ERROR级别日志，应用正常启动

# 检查系统日志
journalctl -u vgop -n 20
# 预期结果: 服务启动成功日志
```

### 验证结果记录

| 验证项目 | 验证结果 | 备注 |
|----------|----------|------|
| JDK环境 | □ 通过 □ 失败 | _________________ |
| 数据库表创建 | □ 通过 □ 失败 | _________________ |
| 应用启动 | □ 通过 □ 失败 | _________________ |
| 健康检查 | □ 通过 □ 失败 | _________________ |
| 数据库连接 | □ 通过 □ 失败 | _________________ |
| SFTP连接 | □ 通过 □ 失败 | _________________ |
| 日志输出 | □ 通过 □ 失败 | _________________ |

## 倒回步骤

### 倒回场景

当部署过程中出现以下情况时，需要执行倒回操作：
1. 应用启动失败且无法修复
2. 数据库连接异常且影响现有业务
3. 系统资源消耗过高影响其他服务
4. 关键功能验证失败

### 倒回操作步骤

#### 1. 停止VGOP服务

**操作时长**: 2分钟

```bash
# 1.1 停止应用服务
sudo systemctl stop vgop
sudo systemctl disable vgop

# 1.2 确认服务已停止
sudo systemctl status vgop
ps -ef | grep vgop-service

# 1.3 释放端口
netstat -tlnp | grep 8080
```

#### 2. 清理应用文件

**操作时长**: 5分钟

```bash
# 2.1 备份日志文件（用于问题分析）
mkdir -p /tmp/vgop-rollback-$(date +%Y%m%d_%H%M%S)
cp -r /opt/vgop/logs/* /tmp/vgop-rollback-$(date +%Y%m%d_%H%M%S)/

# 2.2 删除应用目录
rm -rf /opt/vgop

# 2.3 删除systemd服务文件
sudo rm -f /etc/systemd/system/vgop.service
sudo systemctl daemon-reload
```

#### 3. 清理数据库表（可选）

**操作时长**: 3分钟

```bash
# 3.1 删除创建的业务表（谨慎操作）
dbaccess bms << EOF
DROP TABLE IF EXISTS vgop_task_execution_log;
DROP TABLE IF EXISTS vgop_metrics_history;
DROP TABLE IF EXISTS vgop_validation_alerts;
EOF

# 3.2 验证表已删除
dbaccess bms << EOF
SELECT tabname FROM systables WHERE tabname LIKE 'vgop_%';
EOF
```

#### 4. 清理用户和环境

**操作时长**: 3分钟

```bash
# 4.1 删除应用用户（可选）
sudo userdel -r vgop

# 4.2 清理JDK环境（如果是专门为此项目安装）
# 注意：如果其他应用也使用此JDK，请勿删除
sudo rm -rf /opt/java
sudo sed -i '/JAVA_HOME.*\/opt\/java/d' /etc/profile
sudo sed -i '/PATH.*JAVA_HOME/d' /etc/profile
```

#### 5. 验证倒回结果

**操作时长**: 2分钟

```bash
# 5.1 确认服务已清理
sudo systemctl status vgop
# 预期结果: Unit vgop.service could not be found

# 5.2 确认端口已释放
netstat -tlnp | grep 8080
# 预期结果: 无输出

# 5.3 确认进程已清理
ps -ef | grep vgop
# 预期结果: 仅显示grep进程本身

# 5.4 确认目录已清理
ls -la /opt/ | grep vgop
# 预期结果: 无输出
```

### 倒回后的恢复建议

1. **问题分析**: 分析备份的日志文件，确定部署失败的根本原因
2. **环境检查**: 重新检查服务器环境、网络连接、权限配置等
3. **配置修正**: 根据问题分析结果，修正配置文件中的错误参数
4. **重新部署**: 在问题解决后，可以重新执行部署流程

## 注意事项和风险提示

### 部署前准备

1. **备份现有环境**: 虽然是新部署，但建议备份相关配置文件
2. **确认资源**: 确保服务器有足够的CPU、内存、磁盘空间
3. **网络测试**: 提前测试到数据库和SFTP服务器的网络连通性
4. **权限确认**: 确认操作用户具有必要的系统权限

### 部署过程注意事项

1. **配置文件敏感信息**: 数据库密码、SFTP密码等敏感信息需要妥善保管
2. **端口冲突**: 确认8080端口未被其他应用占用
3. **防火墙设置**: 确认防火墙允许相关端口的访问
4. **时间同步**: 确保服务器时间准确，影响定时任务执行

### 部署后监控

1. **资源监控**: 持续监控CPU、内存、磁盘使用情况
2. **日志监控**: 定期检查应用日志，及时发现异常
3. **功能监控**: 定期执行健康检查，确保服务正常运行
4. **性能监控**: 监控数据处理性能，确保满足业务要求

## 附录

### 附录A: 配置文件模板

#### A.1 完整的application-prod.yml配置模板

```yaml
# VGOP数据校验系统生产环境配置文件
spring:
  application:
    name: vgop-service
  profiles:
    active: prod

  # 数据源配置
  datasource:
    url: jdbc:informix-sqli://[数据库IP]:[端口]/bms:INFORMIXSERVER=[服务器名];DB_LOCALE=zh_CN.UTF8;CLIENT_LOCALE=zh_CN.UTF8
    username: [数据库用户名]
    password: [数据库密码]
    driver-class-name: com.informix.jdbc.IfxDriver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false

# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /vgop
    encoding:
      charset: UTF-8
      enabled: true
      force: true

# MyBatis配置
mybatis:
  mapper-locations: classpath:mapper/**/*.xml
  type-aliases-package: com.vgop.service.entity
  configuration:
    map-underscore-to-camel-case: true
    default-fetch-size: 100
    default-statement-timeout: 30
    cache-enabled: true

# 日志配置
logging:
  level:
    root: INFO
    com.vgop.service: INFO
    org.springframework: WARN
  file:
    name: /opt/vgop/logs/vgop-service.log
    max-size: 100MB
    max-history: 30

# VGOP应用核心配置
app:
  # 定时任务触发时间
  schedules:
    daily-cron: "0 0 0 * * ?"    # 每日凌晨0点
    monthly-cron: "0 0 5 1 * ?"  # 每月1日凌晨5点

  # 根目录配置
  base-path:
    export-root: "/opt/vgop/data/VGOPdata/datafile/"
    log-root: "/opt/vgop/logs/"
    backup-root: "/opt/vgop/backup/"

  # SFTP配置
  sftp:
    host: "[SFTP服务器IP]"
    port: [SFTP端口]
    username: "[SFTP用户名]"
    password: "[SFTP密码]"
    remote-base-path: "/upload/"
    connection-timeout: 30000
    retry-times: 3

  # 文件处理配置
  file-processing:
    max-rows-per-file: 2000000
    default-delimiter: "|"
    output-delimiter: "\200"
    line-ending: "\r"
    add-line-number: true
    line-number-delimiter: "|"

  # 告警配置
  alert:
    storage:
      enable-database: true
      enable-file: false
      alert-file-path: "/opt/vgop/data/alerts/"
    upload-deadline:
      daily: 8
      monthly: 8
    thresholds:
      max-alerts-per-batch: 1000

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
```

#### A.2 logback-spring.xml日志配置模板

```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <springProfile name="prod">
        <!-- 生产环境日志配置 -->
        <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>/opt/vgop/logs/vgop-service.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>/opt/vgop/logs/vgop-service.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
                <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                    <maxFileSize>100MB</maxFileSize>
                </timeBasedFileNamingAndTriggeringPolicy>
                <maxHistory>30</maxHistory>
            </rollingPolicy>
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>

        <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>/opt/vgop/logs/vgop-service-error.log</file>
            <filter class="ch.qos.logback.classic.filter.LevelFilter">
                <level>ERROR</level>
                <onMatch>ACCEPT</onMatch>
                <onMismatch>DENY</onMismatch>
            </filter>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>/opt/vgop/logs/vgop-service-error.%d{yyyy-MM-dd}.log</fileNamePattern>
                <maxHistory>30</maxHistory>
            </rollingPolicy>
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>

        <root level="INFO">
            <appender-ref ref="FILE"/>
            <appender-ref ref="ERROR_FILE"/>
        </root>
    </springProfile>
</configuration>
```

### 附录B: 常见问题解决方案

#### B.1 JDK安装问题

**问题**: JDK安装后java命令不可用
**解决方案**:
```bash
# 检查JAVA_HOME环境变量
echo $JAVA_HOME

# 重新设置环境变量
export JAVA_HOME=/opt/java
export PATH=$JAVA_HOME/bin:$PATH

# 永久设置
echo 'export JAVA_HOME=/opt/java' >> ~/.bashrc
echo 'export PATH=$JAVA_HOME/bin:$PATH' >> ~/.bashrc
source ~/.bashrc
```

#### B.2 数据库连接问题

**问题**: 数据库连接失败
**解决方案**:
```bash
# 1. 检查数据库服务状态
sudo systemctl status informix

# 2. 测试网络连通性
telnet [数据库IP] [端口]

# 3. 检查数据库用户权限
dbaccess bms << EOF
SELECT USER FROM systables WHERE tabid = 1;
EOF

# 4. 验证JDBC连接字符串
java -cp /opt/vgop/lib/ifxjdbc.jar TestConnection
```

#### B.3 应用启动失败

**问题**: 应用启动时出现端口占用
**解决方案**:
```bash
# 1. 查找占用端口的进程
netstat -tlnp | grep 8080
lsof -i :8080

# 2. 终止占用进程
kill -9 [进程ID]

# 3. 或者修改应用端口
vi /opt/vgop/config/application-prod.yml
# 修改 server.port: 8081
```

#### B.4 SFTP连接问题

**问题**: SFTP连接超时或认证失败
**解决方案**:
```bash
# 1. 手动测试SFTP连接
sftp [用户名]@[SFTP服务器IP]

# 2. 检查网络连通性
ping [SFTP服务器IP]
telnet [SFTP服务器IP] [SFTP端口]

# 3. 验证用户权限
ssh [用户名]@[SFTP服务器IP] "ls -la /upload/"

# 4. 检查防火墙设置
sudo firewall-cmd --list-all
```

#### B.5 内存不足问题

**问题**: 应用运行时出现OutOfMemoryError
**解决方案**:
```bash
# 1. 增加JVM堆内存
vi /etc/systemd/system/vgop.service
# 修改ExecStart中的内存参数: -Xms4g -Xmx8g

# 2. 重载服务配置
sudo systemctl daemon-reload
sudo systemctl restart vgop

# 3. 监控内存使用
jstat -gc [进程ID]
top -p [进程ID]
```

### 附录C: 运维脚本

#### C.1 服务状态检查脚本

```bash
#!/bin/bash
# vgop-health-check.sh - VGOP服务健康检查脚本

echo "=== VGOP服务健康检查 ==="
echo "检查时间: $(date)"
echo

# 1. 检查服务状态
echo "1. 服务状态检查:"
systemctl is-active vgop
if [ $? -eq 0 ]; then
    echo "✓ VGOP服务运行正常"
else
    echo "✗ VGOP服务未运行"
    exit 1
fi

# 2. 检查端口监听
echo "2. 端口监听检查:"
netstat -tlnp | grep :8080 > /dev/null
if [ $? -eq 0 ]; then
    echo "✓ 端口8080监听正常"
else
    echo "✗ 端口8080未监听"
fi

# 3. 检查健康接口
echo "3. 健康接口检查:"
curl -s http://localhost:8080/vgop/actuator/health | grep '"status":"UP"' > /dev/null
if [ $? -eq 0 ]; then
    echo "✓ 健康检查接口正常"
else
    echo "✗ 健康检查接口异常"
fi

# 4. 检查日志错误
echo "4. 错误日志检查:"
error_count=$(tail -n 100 /opt/vgop/logs/vgop-service.log | grep -c "ERROR")
if [ $error_count -eq 0 ]; then
    echo "✓ 近期无错误日志"
else
    echo "⚠ 发现 $error_count 条错误日志"
fi

echo
echo "=== 健康检查完成 ==="
```

#### C.2 日志清理脚本

```bash
#!/bin/bash
# vgop-log-cleanup.sh - VGOP日志清理脚本

LOG_DIR="/opt/vgop/logs"
BACKUP_DIR="/opt/vgop/backup/logs"
RETENTION_DAYS=30

echo "=== VGOP日志清理开始 ==="
echo "清理时间: $(date)"
echo "日志目录: $LOG_DIR"
echo "保留天数: $RETENTION_DAYS"
echo

# 创建备份目录
mkdir -p $BACKUP_DIR

# 查找并清理超过保留期的日志文件
find $LOG_DIR -name "*.log.*" -type f -mtime +$RETENTION_DAYS -exec ls -la {} \;
find $LOG_DIR -name "*.log.*" -type f -mtime +$RETENTION_DAYS -exec mv {} $BACKUP_DIR/ \;

# 压缩备份的日志文件
cd $BACKUP_DIR
for file in *.log.*; do
    if [ -f "$file" ]; then
        gzip "$file"
        echo "已压缩: $file"
    fi
done

# 清理超过60天的备份文件
find $BACKUP_DIR -name "*.gz" -type f -mtime +60 -delete

echo "=== 日志清理完成 ==="
```

### 附录D: 应急处理流程

#### D.1 服务异常重启流程

1. **发现异常**: 通过监控或健康检查发现服务异常
2. **快速诊断**: 检查日志文件确定异常原因
3. **尝试重启**: 执行服务重启命令
4. **验证恢复**: 确认服务正常运行
5. **问题跟踪**: 记录异常原因和处理过程

#### D.2 数据库连接异常处理

1. **确认问题**: 验证数据库连接是否真的异常
2. **检查网络**: 测试到数据库服务器的网络连通性
3. **重启连接**: 重启应用服务重新建立连接
4. **联系DBA**: 如果问题持续，联系数据库管理员
5. **业务通知**: 及时通知相关业务方

#### D.3 磁盘空间不足处理

1. **空间检查**: 确认磁盘使用情况
2. **日志清理**: 清理历史日志文件
3. **数据归档**: 归档或删除过期数据文件
4. **扩容申请**: 如需要，申请磁盘扩容
5. **监控加强**: 加强磁盘空间监控

---

**文档版本**: v1.0
**最后更新**: 2025-01-05
**文档状态**: 待审核
**联系人**: 系统架构师
**紧急联系**: [联系方式]
